<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
    </linearGradient>
    <pattern id="circuit" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="none"/>
      <circle cx="20" cy="20" r="2" fill="#667eea" opacity="0.3"/>
      <circle cx="80" cy="20" r="2" fill="#667eea" opacity="0.3"/>
      <circle cx="20" cy="80" r="2" fill="#667eea" opacity="0.3"/>
      <circle cx="80" cy="80" r="2" fill="#667eea" opacity="0.3"/>
      <line x1="20" y1="20" x2="80" y2="20" stroke="#667eea" stroke-width="1" opacity="0.2"/>
      <line x1="20" y1="80" x2="80" y2="80" stroke="#667eea" stroke-width="1" opacity="0.2"/>
      <line x1="20" y1="20" x2="20" y2="80" stroke="#667eea" stroke-width="1" opacity="0.2"/>
      <line x1="80" y1="20" x2="80" y2="80" stroke="#667eea" stroke-width="1" opacity="0.2"/>
    </pattern>
  </defs>
  
  <rect width="1200" height="600" fill="url(#bgGradient)"/>
  <rect width="1200" height="600" fill="url(#circuit)"/>
  
  <!-- Floating tech elements -->
  <g opacity="0.1">
    <!-- CPU chip -->
    <rect x="100" y="100" width="40" height="40" rx="5" fill="#667eea"/>
    <rect x="110" y="110" width="20" height="20" fill="white"/>
    
    <!-- Raspberry Pi -->
    <rect x="300" y="200" width="60" height="40" rx="5" fill="#C51A4A"/>
    <circle cx="320" cy="220" r="8" fill="white"/>
    <rect x="340" y="210" width="15" height="20" fill="#333"/>
    
    <!-- Camera -->
    <circle cx="500" cy="150" r="25" fill="#333"/>
    <circle cx="500" cy="150" r="15" fill="#667eea"/>
    <circle cx="500" cy="150" r="8" fill="white"/>
    
    <!-- Sensors -->
    <rect x="700" y="300" width="30" height="20" rx="10" fill="#4ecdc4"/>
    <rect x="750" y="320" width="25" height="15" rx="7" fill="#ff6b6b"/>
    
    <!-- Code lines -->
    <rect x="900" y="400" width="200" height="3" fill="#667eea"/>
    <rect x="900" y="410" width="150" height="3" fill="#667eea"/>
    <rect x="900" y="420" width="180" height="3" fill="#667eea"/>
    
    <!-- Network nodes -->
    <circle cx="200" cy="450" r="5" fill="#4ecdc4"/>
    <circle cx="250" cy="480" r="5" fill="#4ecdc4"/>
    <circle cx="300" cy="460" r="5" fill="#4ecdc4"/>
    <line x1="200" y1="450" x2="250" y2="480" stroke="#4ecdc4" stroke-width="2"/>
    <line x1="250" y1="480" x2="300" y2="460" stroke="#4ecdc4" stroke-width="2"/>
  </g>
</svg>
