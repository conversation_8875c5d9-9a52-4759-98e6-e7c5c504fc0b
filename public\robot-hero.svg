<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="10" stdDeviation="10" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="200" cy="200" r="180" fill="url(#robotGradient)" opacity="0.1"/>
  
  <!-- Robot body -->
  <rect x="150" y="180" width="100" height="140" rx="20" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  
  <!-- Robot head -->
  <circle cx="200" cy="140" r="50" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  
  <!-- Eyes -->
  <circle cx="185" cy="130" r="8" fill="#667eea"/>
  <circle cx="215" cy="130" r="8" fill="#667eea"/>
  <circle cx="185" cy="130" r="3" fill="white"/>
  <circle cx="215" cy="130" r="3" fill="white"/>
  
  <!-- Mouth -->
  <rect x="190" y="150" width="20" height="8" rx="4" fill="#333"/>
  
  <!-- Chest panel -->
  <rect x="170" y="200" width="60" height="40" rx="10" fill="#667eea" opacity="0.3"/>
  <circle cx="200" cy="220" r="8" fill="#4ecdc4"/>
  
  <!-- Arms -->
  <rect x="120" y="190" width="25" height="80" rx="12" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  <rect x="255" y="190" width="25" height="80" rx="12" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  
  <!-- Hands -->
  <circle cx="132" cy="280" r="12" fill="url(#bodyGradient)"/>
  <circle cx="268" cy="280" r="12" fill="url(#bodyGradient)"/>
  
  <!-- Legs -->
  <rect x="165" y="320" width="20" height="60" rx="10" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  <rect x="215" y="320" width="20" height="60" rx="10" fill="url(#bodyGradient)" filter="url(#shadow)"/>
  
  <!-- Feet -->
  <ellipse cx="175" cy="390" rx="15" ry="8" fill="#333"/>
  <ellipse cx="225" cy="390" rx="15" ry="8" fill="#333"/>
  
  <!-- Antenna -->
  <line x1="200" y1="90" x2="200" y2="70" stroke="#667eea" stroke-width="3"/>
  <circle cx="200" cy="70" r="5" fill="#ff6b6b"/>
  
  <!-- Tech elements -->
  <rect x="160" y="250" width="80" height="2" fill="#667eea" opacity="0.5"/>
  <rect x="160" y="260" width="60" height="2" fill="#667eea" opacity="0.5"/>
  <rect x="160" y="270" width="70" height="2" fill="#667eea" opacity="0.5"/>
</svg>
