import { useState } from 'react'
import './App.css'

function App() {
  const [activeSection, setActiveSection] = useState('home')
  const [selectedImage, setSelectedImage] = useState(null)

  const features = [
    {
      icon: '🎤',
      title: 'Interaction Vocale',
      description: 'Communication intelligente avec l\'IA Gemini pour des conversations naturelles',
      image: '/voice-icon.svg',
      color: '#667eea'
    },
    {
      icon: '🚧',
      title: 'Détection d\'Obstacles',
      description: 'Navigation autonome avec évitement d\'obstacles en temps réel',
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=300&h=200&fit=crop',
      color: '#ff6b6b'
    },
    {
      icon: '📱',
      title: 'Lecture QR Codes',
      description: 'Reconnaissance et traitement automatique des codes QR',
      image: 'https://images.unsplash.com/photo-1606166187734-a4cb74c2e9b8?w=300&h=200&fit=crop',
      color: '#4ecdc4'
    },
    {
      icon: '👁️',
      title: 'Reconnaissance d\'Objets',
      description: 'Identification et classification d\'objets avec OpenCV',
      image: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=300&h=200&fit=crop',
      color: '#45b7d1'
    },
    {
      icon: '🤖',
      title: 'Déplacement Autonome',
      description: 'Mouvement intelligent et adaptatif dans l\'environnement',
      image: 'https://images.unsplash.com/photo-1561557944-6e7860d1a7eb?w=300&h=200&fit=crop',
      color: '#764ba2'
    },
    {
      icon: '📹',
      title: 'Streaming Vidéo',
      description: 'Transmission vidéo en temps réel pour surveillance et contrôle',
      image: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop',
      color: '#ee5a24'
    }
  ]

  const galleryImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop',
      title: 'Robot en action',
      description: 'Le robot navigue de manière autonome'
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-1561557944-6e7860d1a7eb?w=400&h=300&fit=crop',
      title: 'Composants électroniques',
      description: 'Raspberry Pi et capteurs intégrés'
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=400&h=300&fit=crop',
      title: 'Vision par ordinateur',
      description: 'Reconnaissance d\'objets avec OpenCV'
    },
    {
      id: 4,
      src: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=300&fit=crop',
      title: 'Interface de contrôle',
      description: 'Dashboard de monitoring en temps réel'
    },
    {
      id: 5,
      src: 'https://images.unsplash.com/photo-1606166187734-a4cb74c2e9b8?w=400&h=300&fit=crop',
      title: 'Lecture QR Code',
      description: 'Système de reconnaissance automatique'
    },
    {
      id: 6,
      src: '/robot-hero.svg',
      title: 'Design du robot',
      description: 'Conception et architecture du système'
    }
  ]

  const technologies = [
    { name: 'Raspberry Pi', category: 'Matériel', color: '#C51A4A' },
    { name: 'Python', category: 'Logiciel', color: '#3776AB' },
    { name: 'OpenCV', category: 'Vision', color: '#5C3EE8' },
    { name: 'Flask', category: 'Web', color: '#000000' },
    { name: 'Gemini API', category: 'IA', color: '#4285F4' },
    { name: 'Caméra', category: 'Matériel', color: '#FF6B6B' },
    { name: 'Capteurs', category: 'Matériel', color: '#4ECDC4' },
    { name: 'Moteurs', category: 'Matériel', color: '#45B7D1' }
  ]

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="container">
          <div className="logo">
            <span className="robot-icon">🤖</span>
            <span>Robot Assistant</span>
          </div>
          <nav className="nav">
            <a href="#home" className={activeSection === 'home' ? 'active' : ''}
               onClick={() => setActiveSection('home')}>Accueil</a>
            <a href="#features" className={activeSection === 'features' ? 'active' : ''}
               onClick={() => setActiveSection('features')}>Fonctionnalités</a>
            <a href="#tech" className={activeSection === 'tech' ? 'active' : ''}
               onClick={() => setActiveSection('tech')}>Technologies</a>
            <a href="#demo" className={activeSection === 'demo' ? 'active' : ''}
               onClick={() => setActiveSection('demo')}>Démo</a>
            <a href="#gallery" className={activeSection === 'gallery' ? 'active' : ''}
               onClick={() => setActiveSection('gallery')}>Galerie</a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section id="home" className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1>Robot Assistant Humanoïde</h1>
              <p className="hero-subtitle">
                Projet de fin d'études innovant combinant intelligence artificielle et robotique
              </p>
              <p className="hero-description">
                Un robot intelligent capable d'interagir avec les humains et son environnement,
                équipé de technologies de pointe pour une expérience d'interaction naturelle et intuitive.
              </p>
              <div className="hero-buttons">
                <button className="btn-primary" onClick={() => setActiveSection('demo')}>
                  Voir la Démo
                </button>
                <button className="btn-secondary" onClick={() => setActiveSection('features')}>
                  Découvrir les Fonctionnalités
                </button>
              </div>
            </div>
            <div className="hero-visual">
              <div className="hero-image-container">
                <img src="/robot-hero.svg" alt="Robot Assistant Humanoïde" className="hero-robot-image" />
                <div className="floating-elements">
                  <div className="floating-element" style={{top: '10%', left: '10%'}}>
                    <span>🧠 IA</span>
                  </div>
                  <div className="floating-element" style={{top: '20%', right: '15%'}}>
                    <span>📷 Vision</span>
                  </div>
                  <div className="floating-element" style={{bottom: '30%', left: '5%'}}>
                    <span>🔊 Audio</span>
                  </div>
                  <div className="floating-element" style={{bottom: '15%', right: '10%'}}>
                    <span>⚡ IoT</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="container">
          <h2>Fonctionnalités Avancées</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card" style={{'--accent-color': feature.color}}>
                <div className="feature-image">
                  <img src={feature.image} alt={feature.title} />
                  <div className="feature-overlay">
                    <span className="feature-icon">{feature.icon}</span>
                  </div>
                </div>
                <div className="feature-content">
                  <h3>{feature.title}</h3>
                  <p>{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section id="tech" className="technologies">
        <div className="container">
          <h2>Technologies Utilisées</h2>
          <div className="tech-categories">
            <div className="tech-category">
              <h3>⚙️ Matériel</h3>
              <div className="tech-items">
                {technologies.filter(tech => tech.category === 'Matériel').map((tech, index) => (
                  <span key={index} className="tech-tag" style={{borderColor: tech.color}}>
                    {tech.name}
                  </span>
                ))}
              </div>
            </div>
            <div className="tech-category">
              <h3>💻 Logiciel</h3>
              <div className="tech-items">
                {technologies.filter(tech => tech.category !== 'Matériel').map((tech, index) => (
                  <span key={index} className="tech-tag" style={{borderColor: tech.color}}>
                    {tech.name}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="demo">
        <div className="container">
          <h2>Démonstration</h2>
          <div className="demo-content">
            <div className="demo-video">
              <div className="video-container">
                <iframe
                  width="100%"
                  height="315"
                  src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                  title="Robot Assistant Démonstration"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
              <div className="video-info">
                <h4>🎥 Démonstration en Direct</h4>
                <p>Découvrez le robot en action avec ses capacités d'interaction et de navigation autonome</p>
              </div>
            </div>
            <div className="demo-info">
              <h3>Capacités en Action</h3>
              <ul>
                <li>✅ Reconnaissance vocale et réponses intelligentes</li>
                <li>✅ Navigation autonome dans l'espace</li>
                <li>✅ Détection et évitement d'obstacles</li>
                <li>✅ Interaction avec objets et QR codes</li>
                <li>✅ Streaming vidéo haute qualité</li>
                <li>✅ Interface de contrôle intuitive</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="gallery">
        <div className="container">
          <h2>Galerie du Projet</h2>
          <p className="gallery-subtitle">Découvrez les différentes étapes et composants de notre robot</p>
          <div className="gallery-grid">
            {galleryImages.map((image) => (
              <div key={image.id} className="gallery-item" onClick={() => setSelectedImage(image)}>
                <img src={image.src} alt={image.title} />
                <div className="gallery-overlay">
                  <h4>{image.title}</h4>
                  <p>{image.description}</p>
                  <span className="view-icon">👁️</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <div className="modal-overlay" onClick={() => setSelectedImage(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={() => setSelectedImage(null)}>×</button>
            <img src={selectedImage.src} alt={selectedImage.title} />
            <div className="modal-info">
              <h3>{selectedImage.title}</h3>
              <p>{selectedImage.description}</p>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h4>🤖 Robot Assistant Humanoïde</h4>
              <p>Projet de fin d'études en robotique et intelligence artificielle</p>
            </div>
            <div className="footer-section">
              <h4>Technologies</h4>
              <p>Raspberry Pi • Python • OpenCV • Flask • Gemini AI</p>
            </div>
            <div className="footer-section">
              <h4>Contact</h4>
              <p>Projet académique - 2024</p>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Robot Assistant Humanoïde. Tous droits réservés.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
