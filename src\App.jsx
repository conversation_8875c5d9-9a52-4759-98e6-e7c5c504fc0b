import { useState } from 'react'
import './App.css'

function App() {
  const [activeSection, setActiveSection] = useState('home')

  const features = [
    {
      icon: '🎤',
      title: 'Interaction Vocale',
      description: 'Communication intelligente avec l\'IA Gemini pour des conversations naturelles'
    },
    {
      icon: '🚧',
      title: 'Détection d\'Obstacles',
      description: 'Navigation autonome avec évitement d\'obstacles en temps réel'
    },
    {
      icon: '📱',
      title: 'Lecture QR Codes',
      description: 'Reconnaissance et traitement automatique des codes QR'
    },
    {
      icon: '👁️',
      title: 'Reconnaissance d\'Objets',
      description: 'Identification et classification d\'objets avec OpenCV'
    },
    {
      icon: '🤖',
      title: 'Déplacement Autonome',
      description: 'Mouvement intelligent et adaptatif dans l\'environnement'
    },
    {
      icon: '📹',
      title: 'Streaming Vidéo',
      description: 'Transmission vidéo en temps réel pour surveillance et contrôle'
    }
  ]

  const technologies = [
    { name: 'Raspberry Pi', category: 'Matériel', color: '#C51A4A' },
    { name: 'Python', category: 'Logiciel', color: '#3776AB' },
    { name: 'OpenCV', category: 'Vision', color: '#5C3EE8' },
    { name: 'Flask', category: 'Web', color: '#000000' },
    { name: 'Gemini API', category: 'IA', color: '#4285F4' },
    { name: 'Caméra', category: 'Matériel', color: '#FF6B6B' },
    { name: 'Capteurs', category: 'Matériel', color: '#4ECDC4' },
    { name: 'Moteurs', category: 'Matériel', color: '#45B7D1' }
  ]

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="container">
          <div className="logo">
            <span className="robot-icon">🤖</span>
            <span>Robot Assistant</span>
          </div>
          <nav className="nav">
            <a href="#home" className={activeSection === 'home' ? 'active' : ''}
               onClick={() => setActiveSection('home')}>Accueil</a>
            <a href="#features" className={activeSection === 'features' ? 'active' : ''}
               onClick={() => setActiveSection('features')}>Fonctionnalités</a>
            <a href="#tech" className={activeSection === 'tech' ? 'active' : ''}
               onClick={() => setActiveSection('tech')}>Technologies</a>
            <a href="#demo" className={activeSection === 'demo' ? 'active' : ''}
               onClick={() => setActiveSection('demo')}>Démo</a>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section id="home" className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1>Robot Assistant Humanoïde</h1>
              <p className="hero-subtitle">
                Projet de fin d'études innovant combinant intelligence artificielle et robotique
              </p>
              <p className="hero-description">
                Un robot intelligent capable d'interagir avec les humains et son environnement,
                équipé de technologies de pointe pour une expérience d'interaction naturelle et intuitive.
              </p>
              <div className="hero-buttons">
                <button className="btn-primary" onClick={() => setActiveSection('demo')}>
                  Voir la Démo
                </button>
                <button className="btn-secondary" onClick={() => setActiveSection('features')}>
                  Découvrir les Fonctionnalités
                </button>
              </div>
            </div>
            <div className="hero-visual">
              <div className="robot-display">
                <div className="robot-body">
                  <div className="robot-head">
                    <div className="robot-eyes">
                      <div className="eye left"></div>
                      <div className="eye right"></div>
                    </div>
                    <div className="robot-mouth"></div>
                  </div>
                  <div className="robot-chest">
                    <div className="status-light"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features">
        <div className="container">
          <h2>Fonctionnalités Avancées</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">{feature.icon}</div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section id="tech" className="technologies">
        <div className="container">
          <h2>Technologies Utilisées</h2>
          <div className="tech-categories">
            <div className="tech-category">
              <h3>⚙️ Matériel</h3>
              <div className="tech-items">
                {technologies.filter(tech => tech.category === 'Matériel').map((tech, index) => (
                  <span key={index} className="tech-tag" style={{borderColor: tech.color}}>
                    {tech.name}
                  </span>
                ))}
              </div>
            </div>
            <div className="tech-category">
              <h3>💻 Logiciel</h3>
              <div className="tech-items">
                {technologies.filter(tech => tech.category !== 'Matériel').map((tech, index) => (
                  <span key={index} className="tech-tag" style={{borderColor: tech.color}}>
                    {tech.name}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="demo">
        <div className="container">
          <h2>Démonstration</h2>
          <div className="demo-content">
            <div className="demo-video">
              <div className="video-placeholder">
                <div className="play-button">▶️</div>
                <p>Vidéo de démonstration</p>
                <small>Streaming vidéo en temps réel</small>
              </div>
            </div>
            <div className="demo-info">
              <h3>Capacités en Action</h3>
              <ul>
                <li>✅ Reconnaissance vocale et réponses intelligentes</li>
                <li>✅ Navigation autonome dans l'espace</li>
                <li>✅ Détection et évitement d'obstacles</li>
                <li>✅ Interaction avec objets et QR codes</li>
                <li>✅ Streaming vidéo haute qualité</li>
                <li>✅ Interface de contrôle intuitive</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h4>🤖 Robot Assistant Humanoïde</h4>
              <p>Projet de fin d'études en robotique et intelligence artificielle</p>
            </div>
            <div className="footer-section">
              <h4>Technologies</h4>
              <p>Raspberry Pi • Python • OpenCV • Flask • Gemini AI</p>
            </div>
            <div className="footer-section">
              <h4>Contact</h4>
              <p>Projet académique - 2024</p>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Robot Assistant Humanoïde. Tous droits réservés.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
